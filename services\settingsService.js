const settingsModel = require('../models/settingsModel');

const getCountries = async ()=>{
const countries =  await settingsModel.getCountries();
return countries;

};

const createBank = async (userData,file)=>{
    const checkBank = await settingsModel.findBankByName(userData.name);
    if(checkBank){
      throw new Error(`Bank ${userData.name} already exists`);
    }
    userData['logo'] =  `uploads/${file.filename}`;
    await settingsModel.createBank(userData);
}

const getBanks = async (baseUrl)=>{
    const country = await settingsModel.getCountries();
    const result = await settingsModel.getBanks(baseUrl);
    return result.map(bank => ({
        ...bank,
        country: country.find(x=>x.id === bank.country)
    }));
 };




module.exports = {
    getCountries,
    createBank,
    getBanks
}