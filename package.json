{"name": "ethiopay", "version": "1.0.0", "description": "", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "nodemon server.js"}, "dependencies": {"bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.21.1", "express-validator": "^7.1.0", "firebase-admin": "^12.3.0", "helmet": "^7.1.0", "i18n": "^0.15.1", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.10.2", "nodemailer": "^6.9.16", "nodemon": "^3.0.1", "semver": "^7.6.3", "socket.io": "^4.7.5"}, "author": "", "license": "ISC"}