const pool = require("../config/db");
const createNotifications = async (userData)=>{
    await pool.query(
        "INSERT INTO ethiopay_notifications SET ?",
        userData
      );
}

const getNotifications = async (userId) => {
    const [result] = await pool.query(
        "SELECT * FROM ethiopay_notifications where userId = ?", userId
      );
      return result;
};

const updateStatus = async (id,date)=>{
await pool.query(
 "UPDATE ethiopay_notifications SET status='read',updatedAt=? where id = ?",[date,id]
      );
}

const findNotificationsById = async (id,userId) => {
    const [result] = await pool.query(
        "SELECT * FROM ethiopay_notifications where userId = ? and id=?", [userId, id]
      );
      return result[0];
};






module.exports = {
    createNotifications,
    getNotifications,
    updateStatus,
    findNotificationsById
}