const Notification = require('./schemas/Notification');

const createNotifications = async (userData) => {
  const notification = new Notification(userData);
  await notification.save();
}

const getNotifications = async (userId) => {
  const notifications = await Notification.find({ userId: userId })
    .populate('userId', 'fullName username')
    .sort({ createdAt: -1 });
  return notifications;
};

const updateStatus = async (id, date) => {
  await Notification.findByIdAndUpdate(id, {
    status: 'read',
    updatedAt: date
  });
}

const findNotificationsById = async (id, userId) => {
  const notification = await Notification.findOne({
    _id: id,
    userId: userId
  });
  return notification;
};






module.exports = {
    createNotifications,
    getNotifications,
    updateStatus,
    findNotificationsById
}