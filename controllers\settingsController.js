const settingsService = require("../services/settingsService");
const path = require("path");
const multer = require("multer");
const fs = require("fs");

const uploadDir = "./uploads/";
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir);
}

const storage = multer.diskStorage({
  destination: uploadDir,
  filename: function (req, file, cb) {
    cb(
      null,
      `${file.fieldname}-${Date.now()}${path.extname(file.originalname)}`
    );
  },
});

function checkFileType(file, cb) {
  const filetypes = /jpeg|jpg|png|gif/;
  const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
  const mimetype = filetypes.test(file.mimetype);

  if (mimetype && extname) {
    return cb(null, true);
  } else {
    cb(new Error("Error: Images Only!"));
  }
}

const upload = multer({
  storage: storage,
  limits: { fileSize: 1 * 1024 * 1024 }, // 1MB limit
  fileFilter: function (req, file, cb) {
    checkFileType(file, cb);
  },
}).single("file");

const createBank = async (req, res, next) => {
  upload(req, res, async (err) => {
    if (err) {
       next(err);
    }

    if (!req.file) {
       next(new Error("No file selected!"));
    }

    try {
      // Pass the file path and body to the service
      await settingsService.createBank(req.body, req.file);
       res.success(null, "success");
    } catch (error) {
      console.error("Error during createBank:", error);
        next(error);
    }
  });
};


const getCountries = async (req, res, next) => {
  try {
    const countries = await settingsService.getCountries();
    res.success(countries, "success");
  } catch (error) {
    next(error);
  }
};


const getBanks = async (req, res) => {
  try {
    const baseUrl = `${req.protocol}://${req.get("host")}`;
    const banks = await settingsService.getBanks(baseUrl);
    res.success(banks, "success");
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getCountries,
  createBank,
  getBanks,
};
