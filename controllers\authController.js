
const authService = require("../services/authService");

const createUser = async (req, res,next) => {
  try {
    const userData = {
      fullName: req.body.fullName,
      username: req.body.username,
      password: req.body.password,
    };
    const data = await authService.createUser(userData);
    res.success(data, 'Successfully created!');
  } catch (error) {
    next(error);
  }
};

const signIn = async (req, res,next) => {
    try {
      const data = await authService.login(req.body);
      res.success(data , 'success');
    } catch (error) {
      next(error);
    }
  };

  const signInWithPin = async (req, res,next) => {
    try {
      const data = await authService.loginWithPin(req.body);
      res.success(data , 'success');
    } catch (error) {
      next(error);
    }
  };

  const otpVerification  = async (req, res,next) => {
    try {
     /**
      * verfication for email and phone number
      */
  
     const token = await authService.otpVerification(req.body);
     res.success({token:token}, 'Verified');
    } catch (error) {
      next(error);
    }
  };

  const resetPassword = async (req, res,next) => {
  
    const {newPassword, username }= req.body; 
   try {
     await authService.resetPassword(
       username,
       newPassword,
     );
     res.success(null, 'Successfully updated');
   } catch (error) {
    next(error);
   }
  };

  const getProfile = async (req, res,next) => {
    try {
      const id = req.user.id;   
      const user = await authService.getProfile(id);
      res.success(user , 'success');
    } catch (error) {
      next(error);
    }
  };

  
  const updateProfile = async (req, res,next) => {
    try {
      const {name,country,phone,email} = req.body;
      if(!name || !country){
        const err = new Error("Name or country can not be empty!");
        err.statusCode = 400;
        throw err;
      }
      const body = {fullName: name, country: country, phoneNumber: phone, email: email}
      const id = req.user.id;
      await authService.updateProfile(body,id); //     
      res.success(null , 'Successfully updated profile');
    } catch (error) {
      next(error);
    }
  };

  const changePassword = async (req, res,next) => {
    try{
      const userId = req.user.id;
      const {currentPassword, newPassword} = req.body;
      await authService.changePassword(userId,currentPassword, newPassword);
      res.success(null , 'Successfully updated');
    } catch (error) {
      next(error);
    }
  }
  
  const setPin = async (req, res,next) => {
    try{
      const userId = req.user.id;
      const {pin} = req.body;
      await authService.setPin(userId,pin);
      res.success(null , 'Successfully updated');
    } catch (error) {
      next(error);
    }
  }

  const stateEmailNotificationSetting = async (req, res,next) => {
    try{
      const userId = req.user.id;
      await authService.emailNotificationStatus(userId);
      res.success(null , 'Successfully updated');
    } catch (error) {
      next(error);
    }
  }
  const stateSmsNotificationSetting = async (req, res,next) => {
    try{
      const userId = req.user.id;
      await authService.smsNotificationStatus(userId);
      res.success(null , 'Successfully updated');
    } catch (error) {
      next(error);
    }
  }

  const statePushNotificationSetting = async (req, res,next) => {
    try{
      const userId = req.user.id;
      await authService.pushNotificationStatus(userId);
      res.success(null , 'Successfully updated');
    } catch (error) {
      next(error);
    }
  }

  
  const notificationSettings = async (req, res,next) => {
    try{
      const userId = req.user.id;
      const settings = await authService.notificationSettingsState(userId);
      res.success(settings , 'success');
    } catch (error) {
      next(error);
    }
  }
  
  
  const getPermissions = async (req, res) => {
    try {
      const id = req.user.id;
      const permissions = await authService.getPermissions(id);
      res.status(200).json(permissions);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  };

  const getUserPermissions = async (req, res) => {
    try {
      const userId = req.user.id;
      const id = req.params.id;
      const permissions = await authService.getUserPermissions(userId,id);
      res.status(200).json(permissions);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  };

  const getUsers = async (req, res) => {
    try {
     const users = await authService.getUsers();
      res.status(200).json(users);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  };

  
  const updateUser = async (req, res) => {
    try {
      const userId = req.user.id;
      const id = req.params.id;
      await authService.updateUser(req.body.fullName,req.body.username,id,userId);
      res.status(200).json({ message: "Successfully created!", status: 200 });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  };

  const updateUserStatus = async (req, res) => {
    try {
      const userId = req.user.id;
      const id = req.params.id;
      await authService.updateUserStatus(req.body.status, id,userId);
      res.status(200).json({ message: "Successfully created!", status: 200 });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  };

  const createPermission = async (req, res) => {
    try {
      const userId = req.user.id;
      const body = req.body;
      await authService.createPermission(body,userId);
      res.status(200).json({ message: "Successfully created!", status: 200 });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  };
  
  const deletePermission = async (req, res) => {
    try {
      const id = req.params.id;
      const userId = req.user.id;
      await authService.deletePermission(id,userId);
      res.status(200).json({ message: "Successfully deleted!", status: 200 });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  };


module.exports = {
createUser,
 signIn,
 otpVerification,
 resetPassword,
 getProfile,
 updateProfile,
 changePassword,
 setPin,
 signInWithPin,
 stateEmailNotificationSetting,
 statePushNotificationSetting,
 stateSmsNotificationSetting,
 notificationSettings,
 getPermissions,
 getUsers,
 updateUser,
 updateUserStatus,
 getUserPermissions,
 createPermission,
 deletePermission
}
