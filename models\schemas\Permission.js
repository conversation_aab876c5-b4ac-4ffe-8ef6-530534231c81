const mongoose = require('mongoose');

const permissionSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  nav_menuId: {
    type: String,
    required: true,
    trim: true
  },
  menuName: {
    type: String,
    trim: true
  },
  permissions: {
    read: {
      type: Boolean,
      default: false
    },
    write: {
      type: Boolean,
      default: false
    },
    update: {
      type: Boolean,
      default: false
    },
    delete: {
      type: Boolean,
      default: false
    }
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Create indexes
permissionSchema.index({ userId: 1 });
permissionSchema.index({ nav_menuId: 1 });
permissionSchema.index({ userId: 1, nav_menuId: 1 }, { unique: true });

module.exports = mongoose.model('Permission', permissionSchema);
