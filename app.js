const express = require('express');
const cors = require('cors');
const authRoutes = require('./routes/routes');
const responseFormatter = require('./middlewares/responseMiddleware');
const errorHandler = require('./middlewares/errorHandler');
const app = express();

app.use(express.json());
app.use(responseFormatter);
app.use('/uploads', express.static('uploads'));
app.use(cors());
app.use('/api', authRoutes);
app.use(errorHandler);

module.exports = app;
