const notificationsService = require("../services/notificationsService");

const getNotifications = async (req, res, next) => {
  try {
   
    const id = req.user.id;
    const notification = await notificationsService.getNotifications(id);
    res.success(notification, "success");
  } catch (error) {
    next(error);
  }
};

const updateNotifications = async (req, res, next) => {
  try {
    const id = req.params.id;
    const userId = req.user.id;
    await notificationsService.updateNotification(id,userId);
    res.success(null, "Successfully updated");
  } catch (error) {
    next(error);
  }
};

module.exports = {
    getNotifications,
    updateNotifications
}