const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
  fullName: {
    type: String,
    required: true,
    trim: true
  },
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    index: true
  },
  password: {
    type: String,
    required: true
  },
  phoneNumber: {
    type: String,
    trim: true
  },
  email: {
    type: String,
    trim: true,
    lowercase: true
  },
  country: {
    type: String,
    trim: true
  },
  pin: {
    type: String
  },
  status: {
    type: Number,
    default: 1 // 1 = active, 0 = inactive
  },
  emailNotification: {
    type: Boolean,
    default: false
  },
  smsNotification: {
    type: Boolean,
    default: false
  },
  pushNotification: {
    type: Boolean,
    default: false
  }
}, {
  timestamps: true // This adds createdAt and updatedAt automatically
});

// Create additional indexes for better performance
userSchema.index({ email: 1 });
userSchema.index({ phoneNumber: 1 });

module.exports = mongoose.model('User', userSchema);
