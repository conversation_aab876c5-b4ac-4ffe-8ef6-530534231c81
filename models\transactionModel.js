const pool = require("../config/db");

const createTransaction = async (userData) => {
  const[result] = await pool.query(
    "INSERT INTO ethiopay_transactions SET ?",
    userData
  );
  return result;
};

const getTransactions = async (userId) => {
  const [result] = await pool.query(
    "SELECT * FROM ethiopay_transactions where id = ?", userId
  ); 
  return result;
};

module.exports = {
  createTransaction,
  getTransactions
}