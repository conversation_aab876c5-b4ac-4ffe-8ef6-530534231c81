const Transaction = require('./schemas/Transaction');

const createTransaction = async (userData) => {
  const transaction = new Transaction(userData);
  const result = await transaction.save();
  return result;
};

const getTransactions = async (userId) => {
  const transactions = await Transaction.find({ userId: userId })
    .populate('userId', 'fullName username')
    .sort({ createdAt: -1 });
  return transactions;
};

module.exports = {
  createTransaction,
  getTransactions
}