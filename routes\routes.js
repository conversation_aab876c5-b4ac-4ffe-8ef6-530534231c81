const express = require('express');
const authMiddleware = require('../middlewares/authMiddleware');
const authController = require('../controllers/authController');
const otpController = require('../controllers/otpController');
const settingsController = require('../controllers/settingsController');
const notificationController = require('../controllers/notificationsController');
const transactionController = require('../controllers/transactionController');
const router = express.Router();
/**
 * @users controller
 */

router.post('/create/user', authController.createUser);
router.post('/signIn', authController.signIn);
router.post('/otp/send/sms', otpController.sendOTPSMS);
router.post('/otp/send/email', otpController.sendOTPEmail);
router.post('/verify/otp', authController.otpVerification);
router.get('/settings/countries', settingsController.getCountries);
router.post('/resetPassword',authMiddleware, authController.resetPassword);
router.get('/user',authMiddleware, authController.getProfile);
router.put('/user',authMiddleware, authController.updateProfile);
router.post('/user/change/password',authMiddleware, authController.changePassword);
router.post('/user/set/pin',authMiddleware, authController.setPin);
router.get('/users',authMiddleware, authController.getUsers);
router.put('/update/user/:id',authMiddleware, authController.updateUser);
router.put('/update/user/status/:id',authMiddleware, authController.updateUserStatus);
/***
 * notifications 
 */
router.get('/notifications',authMiddleware, notificationController.getNotifications);
router.put('/notifications/:id',authMiddleware, notificationController.updateNotifications);

/**
 * banks settings
 */
router.get('/banks', settingsController.getBanks);
router.post('/bank', settingsController.createBank);
router.post('/createtransaction', authMiddleware, transactionController.createTransaction);
router.get('/gettransactions',authMiddleware, transactionController.getTransactions);
/**
 *  notifications settings
 */

router.put('/email/notification/setting/state',authMiddleware, authController.stateEmailNotificationSetting);
router.put('/sms/notification/setting/state',authMiddleware, authController.stateSmsNotificationSetting);
router.put('/push/notification/setting/state',authMiddleware, authController.statePushNotificationSetting);
router.get('/notification/setting/states',authMiddleware, authController.notificationSettings);

/**
 * permissions
 */
router.get('/permissions',authMiddleware, authController.getPermissions);
router.get('/permissions/:id',authMiddleware, authController.getUserPermissions);
router.post('/create/permission',authMiddleware, authController.createPermission);
router.delete('/delete/permission/:id',authMiddleware, authController.deletePermission);
module.exports = router;