const notificationModel = require('../models/notificationsModel');

const createNotification = async (data)=>{
 data['createdAt'] = new Date();
 data['status']='unread';
 await notificationModel.createNotifications(data);
}

const getNotifications = async (userId)=>{
 const notifications = await notificationModel.getNotifications(userId);
 return notifications;
}



const updateNotification = async (id,userId)=>{
    const user = await notificationModel.findNotificationsById(id,userId);
     if(!user){
        const err = new Error("id not found");
        err.statusCode = 400;
        throw err;
     }
    
    await notificationModel.updateStatus(id,new Date());
}
   
module.exports = {
    createNotification,
    getNotifications,
    updateNotification
}