const mongoose = require('mongoose');

const countrySchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  code: {
    type: String,
    required: true,
    unique: true,
    uppercase: true,
    trim: true,
    index: true
  },
  dialCode: {
    type: String,
    trim: true
  },
  currency: {
    type: String,
    trim: true
  },
  currencySymbol: {
    type: String,
    trim: true
  },
  flag: {
    type: String,
    trim: true
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Create additional indexes
countrySchema.index({ name: 1 });

module.exports = mongoose.model('Country', countrySchema);
