const nodemailer = require("nodemailer");
require("dotenv").config();
const emailHost = process.env.emailHost;
const emailAuthUser = process.env.emailAuthUser;
const emailAuthPassword = process.env.emailAuthPassword;
const userModel = require("../models/userModels");
const jwt = require("jsonwebtoken");
const { v4: uuidv4 } = require('uuid');
const sendOTPEmail = async (data) => {
  const user = await userModel.findUserByPhoneOREmail(data.email);
  if(!user){
    const err = new Error(`No account found for ${data.email}`);
    err.statusCode = 404;
    throw err;
  }

  const code = generateRandomNumber();
  const template = `
    <!DOCTYPE html>
<html>
<head>
    <title>OTP Verification</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            background-color: #f4f4f4;
            padding: 20px;
        }
        .container {
            background-color: #ffffff;
            max-width: 600px;
            margin: auto;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .otp {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }
        .footer {
            margin-top: 20px;
            font-size: 12px;
            color: #777;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>Your OTP Code for Verification</h2>
        <p>Dear ${user.fullName},</p>
        <p>To continue with your [specific action, e.g., "login", "transaction"], please use the following One-Time Password (OTP):</p>
        <p class="otp">${code}</p>
        <p><strong>Note:</strong> This code is valid for 10 mins and can only be used once. For your security, do not share this OTP with anyone.</p>
        <p>If you did not request this code, please contact our support team immediately.</p>
        <div class="footer">
            <p>Thank you for using EthioPay.</p>
        </div>
    </div>
</body>
</html>
    `;
  _sendEmail(data.email, "Email verification", template);
  await userModel.updateOTPByEmail(data.email, code, new Date());
 
};



function generateRandomNumber() {
  // Generate a random number between 100000 and 999999
  return Math.floor(100000 + Math.random() * 900000);
}

function _sendEmail(to, subject, emailTemplate) {
  const transporter = nodemailer.createTransport({
    host: emailHost,
    port: 587,
    secure: false,
    auth: {
      user: emailAuthUser,
      pass: emailAuthPassword,
    },
  });

  const mailOptions = {
    from: emailAuthUser,
    to: to,
    subject: subject,
    html: emailTemplate,
  };

  transporter.sendMail(mailOptions, (error, info) => {
    if (error) {
      console.error("Error sending email:", error);
    } else {
      console.log("Email sent:", info.response);
    }
  });
}

module.exports = {
  sendOTPEmail,
};
