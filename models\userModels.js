const User = require('./schemas/User');
const Permission = require('./schemas/Permission');

const createUser = async (userData) => {
  const user = new User(userData);
  const savedUser = await user.save();
  return savedUser;
};

const findUserByUsername = async (username) => {
  const user = await User.findOne({ username: username });
  return user;
};


const getUsers = async () => {
  const users = await User.find({}, 'fullName username status createdAt updatedAt');
  return users;
};










const changePassword = async (userId, password) => {
  await User.findByIdAndUpdate(userId, { password: password });
};

const getUserById = async (userId) => {
  const user = await User.findById(userId);
  return user;
};

const updateUsers = async (name, username, id) => {
  const result = await User.findByIdAndUpdate(id, {
    fullName: name,
    username: username
  }, { new: true });
  return result;
};

const changeUserStatus = async (status, id) => {
  const result = await User.findByIdAndUpdate(id, { status: status }, { new: true });
  return result;
};

const updateProfile = async (profile, userId) => {
  const updateData = {};

  // Add fields to update only if they are not empty
  if (profile.fullName) {
    updateData.fullName = profile.fullName;
  }
  if (profile.country) {
    updateData.country = profile.country;
  }
  if (profile.phoneNumber) {
    updateData.phoneNumber = profile.phoneNumber;
  }
  if (profile.email) {
    updateData.email = profile.email;
  }

  // Only update if there are fields to update
  if (Object.keys(updateData).length > 0) {
    await User.findByIdAndUpdate(userId, updateData);
  }
};

const updatePin = async (userId, pin) => {
  await User.findByIdAndUpdate(userId, { pin: pin });
};

const createPermission = async (userData) => {
  const permission = new Permission(userData);
  const result = await permission.save();
  return result;
};

const getPermissions = async (userId) => {
  const permissions = await Permission.find({ userId: userId });
  return permissions;
};

const deletePermission = async (id) => {
  const result = await Permission.deleteMany({ nav_menuId: id });
  return result;
};

module.exports = {
  findUserByUsername,
  createUser,
  getUsers,
  changePassword,
  getUserById,
  updateProfile,
  updatePin,
  getPermissions,
  updateUsers,
  changeUserStatus,
  createPermission,
  deletePermission
};
