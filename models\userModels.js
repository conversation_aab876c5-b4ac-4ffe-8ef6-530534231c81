const pool = require("../config/db");
const createUser = async (userData) => {
  const [result] = await pool.query(
    "INSERT INTO ethiopay_users_admin SET ?",
    userData
  );
  return { id: result.insertId, ...userData }; // Return the id and the original data
};

const findUserByUsername = async (username) => {
  const [result] = await pool.query(
    "SELECT * FROM ethiopay_users_admin WHERE username=?",
    [username, username]
  );
  return result[0];
};


const getUsers = async () => {
  const [result] = await pool.query(
    "SELECT id,fullName,username,status FROM ethiopay_users_admin"
  );
  return result;
};


const updateOTPByEmail = async (email, otp, date) => {
  await pool.query(
    "UPDATE ethiopay_users SET otp_code=?,otp_sent_on=? Where email=?",
    [otp, date, email]
  );
};

const getUserByIdByOTPCode = async (userId, code) => {
  const [rows] = await pool.query(
    "SELECT * FROM ethiopay_users WHERE id = ? AND otp_code = ?",
    [userId, code]
  );
  return rows[0];
};



const updateEmailVerified = async (id) => {
  await pool.query("UPDATE ethiopay_users SET emailVerified=1 Where id=?", [
    id,
  ]);
};

const changePassword = async (userId, password) => {
  await pool.query("update ethiopay_users set password=? where id=?", [
    password,
    userId,
  ]);
};

const getUserById = async (userId) => {
  const [result] = await pool.query(
    "SELECT * FROM ethiopay_users_admin Where id=?",
    userId
  );
  return result[0];
};

const updateUsers = async (name, username,id) => {
  const result = await pool.query(
    "UPDATE ethiopay_users_admin SET fullName=?,username=? WHERE id=?",
    [name,username, id]
  );
  return result;
};

const changeUserStatus = async (status, id) => {
  const result = await pool.query(
    "UPDATE ethiopay_users_admin SET status=? WHERE id=?",
    [status, id]
  );
  return result;
};

const updateProfile = async (profile, userId) => {
  const fields = [];
  const values = [];

  // Add fields to update only if they are not empty
  if (profile.fullName) {
    fields.push("fullName = ?");
    values.push(profile.fullName);
  }
  if (profile.country) {
    fields.push("country = ?");
    values.push(profile.country);
  }
  if (profile.phoneNumber) {
    fields.push("phoneNumber = ?");
    values.push(profile.phoneNumber);
    fields.push("phoneVerified = 0");
  }
  if (profile.email) {
    fields.push("email = ?");
    values.push(profile.email);
    fields.push("emailVerified = 0");
  }

  // Ensure fields exist to update
  if (fields.length > 0) {
    // Add userId for the WHERE clause
    values.push(userId);

    const query = `UPDATE ethiopay_users SET ${fields.join(", ")} WHERE id = ?`;

    await pool.query(query, values);
  }
};

const updatePin = async (userId, pin) => {
  await pool.query("update ethiopay_users set pin=? where id=?", [
    pin,
    userId,
  ]);
};

const createPermission = async (userData) => {
  const [result] = await pool.query(
    "INSERT INTO ethiopay_admin_permissions SET ?",
    userData
  );
  return result;
};

const getPermissions = async (userId) => {
  const [result] = await pool.query(
    "SELECT * FROM ethiopay_admin_permissions WHERE userId=?",
    userId
  );
  return result;
};

const deletePermission = async (id) => {
  const result = await pool.query(
    "DELETE FROM ethiopay_admin_permissions  WHERE nav_menuId=?",
    [id]
  );
  return result;
};

module.exports = {
  findUserByUsername,
  createUser,
  getUsers,
  getUserByIdByOTPCode,
  updateOTPByEmail,
  updateEmailVerified,
  changePassword,
  getUserById,
  updateProfile,
  updatePin,
  getPermissions,
  updateUsers,
  changeUserStatus,
  createPermission,
  deletePermission
};
