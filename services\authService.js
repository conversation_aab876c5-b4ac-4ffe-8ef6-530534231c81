
const userModel = require('../models/userModels');
const settingsModel = require('../models/settingsModel');
const notificationsService = require('../services/notificationsService');
const bcrypt = require("bcryptjs");
const jwt = require("jsonwebtoken");
const { v4: uuidv4 } = require('uuid');
const createUser = async (userData) => {
   const userbyUsername = await userModel.findUserByUsername(userData.username);
   if(userbyUsername){
    const err = new Error("The User are already existing!");
    err.statusCode = 400;
    throw err;
   }
    const hashedPassword = await bcrypt.hash(userData.password, 8);
    userData.password = hashedPassword;
    userData["status"] = 1;
    userData["createdAt"]= new Date();
    const user = await userModel.createUser(userData);
   
    const token = jwt.sign({ id: user._id,jti: uuidv4() }, process.env.JWT_SECRET, {
      expiresIn: "24h",
    });

    const profile ={
      id: user._id,
      name: user.fullName,
      phone: user.phoneNumber,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    }
    return { token, profile, status: 200 };
  };

  const getProfile = async (userId) => {
    const user = await userModel.getUserById(userId);
    const profile ={
      id: user._id,
      name: user.fullName,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    }
    return profile;
  };

  const updateProfile =  async (userData,userId) => {
    const user = await userModel.getUserById(userId);
    const profile = {
      fullName: userData.fullName,
      country: userData.country,
      phoneNumber: userData.phoneNumber == user.phoneNumber?0:userData.phoneNumber,
      email:userData.email == user.email?0:userData.email,
      updatedAt: new Date()
    }

    await userModel.updateProfile(profile,userId);
  };




  const login = async ({ username, password }) => {
    const user = await userModel.findUserByUsername(username);
    if (!user) {
      const error = new Error("Invalid username or password");
      error.statusCode = 400;
      throw error;
    }
    if (!user.status) {
      const err = new Error("The Account is deactivated.Please Contact your administrator.");
      err.statusCode = 400;
      throw err;
    }

    if (!(await bcrypt.compare(password, user.password))) {
      const error = new Error("Invalid username or password");
      error.statusCode = 400;
      throw error;
    }

    const token = jwt.sign({ id: user._id,jti: uuidv4() }, process.env.JWT_SECRET, {
      expiresIn: "1h",
    });

    const profile ={
      id: user._id,
      name: user.fullName,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    }

    return { token, profile, status: 200 };
   
  };






  


  const changePassword = async (userId, currentPassword, password) => {
    const user = await userModel.getUserById(userId);
   
    if (!user) {
      throw new Error(`User ${userId} not found`);
    }
  
    const isMatch = await bcrypt.compare(currentPassword, user.password);
  
    if (!isMatch) {
      const error = new Error('Invalid password!');
      error.statusCode = 500;
      throw error;
    }
  
    const hashedPassword = await bcrypt.hash(password, 8);
   
    const reset = await userModel.changePassword(userId, hashedPassword);
    return reset;
  };
  const resetPassword = async (username,password)=>{
    const user = await userModel.findUserByUsername(username);
    if(!user) {
      const error = new Error("User not found!");
      error.statusCode = 404;
      throw error;
    }
    const hashedPassword = await bcrypt.hash(password, 8);
    await userModel.changePassword(user._id, hashedPassword);
  }

  const setPin = async (userId, pin) => {
    const user = await userModel.getUserById(userId);
    if (!user) {
      throw new Error(`User ${userId} not found`);
    }
    const hashedPin = await bcrypt.hash(pin, 8);
     await userModel.updatePin(userId, hashedPin);
  };

  const emailNotificationStatus = async (userId) => {
    // Email notifications are no longer supported
    throw new Error("Email notifications are not supported. Email functionality has been removed.");
  };

  const smsNotificationStatus = async (userId) => {
    // SMS notifications are no longer supported
    throw new Error("SMS notifications are not supported. SMS functionality has been removed.");
  };

  const pushNotificationStatus = async (userId) => {
    // Push notifications functionality is not implemented
    throw new Error("Push notifications functionality is not implemented.");
  };

  const notificationSettingsState = async (userId) => {
    // Notification settings are no longer supported
    return {
      emailNotification: false,
      smsNotification: false,
      pushNotification: false,
    };
  };
  

  const getPermissions = async (userId) => {
    const permissions = await userModel.getPermissions(userId);
    return permissions;
  };

  const getUserPermissions = async (userId,id) => {
    const user = await userModel.getUserById(userId);
    if(!user){
     throw new Error(`something went wrong!`);
    }
    const permissions = await userModel.getPermissions(id);
    return permissions;
  };

  const getUsers = async () => {
   const users = await userModel.getUsers();
   return users??[];
  }

  const updateUser = async (name,username,id,userId) => {
    const user = await userModel.getUserById(userId);
    if(!user){
     throw new Error(`something went wrong!`);
    }
    await userModel.updateUsers(name,username, id);
  };

  const updateUserStatus = async (status, id,userId) => {
    const user = await userModel.getUserById(userId);
    if(!user){
     throw new Error(`something went wrong!`);
    }
    await userModel.changeUserStatus(status, id);
  };

  const createPermission = async (userData,userId) => {
    const user = await userModel.getUserById(userId);
    if(!user){
     throw new Error(`something went wrong!`);
    }
    userData.Menu.forEach(async (element) => {
      let data = {
        nav_menuId: element,
        userId: userData.userId,
      };
      await userModel.createPermission(data);
    });
  };
  
  const deletePermission = async (id,userId) => {
    const user = await userModel.getUserById(userId);
    if(!user){
     throw new Error(`something went wrong!`);
    }
    await userModel.deletePermission(id);
  };
  
  
  module.exports = {
    createUser,
    login,
    resetPassword,
    getProfile,
    updateProfile,
    changePassword,
    setPin,
    emailNotificationStatus,
    smsNotificationStatus,
    pushNotificationStatus,
    notificationSettingsState,
    getPermissions,
    getUsers,
    updateUser,
    updateUserStatus,
    getUserPermissions,
    createPermission,
    deletePermission
  }