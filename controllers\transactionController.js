const transactionService = require('../services/transactionService');

const createTransaction = async (req, res, next) => {
  try {
    const data = req.body;
    await transactionService.createTransaction(data);
    res.success(null, 'Successfully created');
  } catch (error) {
    next(error);
  }
};

const getTransactions = async (req, res, next) => {
  try {
    const id = req.user.id;
    const transactions = await transactionService.getTransactions(id);
    res.success(transactions, 'success');
  } catch (error) {
    next(error);
  }
};

module.exports = {
  createTransaction,
  getTransactions
}