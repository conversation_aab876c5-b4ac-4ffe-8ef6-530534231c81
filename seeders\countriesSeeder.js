const mongoose = require('mongoose');
const Country = require('../models/schemas/Country');
require('dotenv').config();

const countries = [
  {
    name: 'Ethiopia',
    code: 'ET',
    dialCode: '+251',
    currency: 'ETB',
    currencySymbol: 'Br',
    flag: '🇪🇹'
  },
  {
    name: 'United States',
    code: 'US',
    dialCode: '+1',
    currency: 'USD',
    currencySymbol: '$',
    flag: '🇺🇸'
  },
  {
    name: 'United Kingdom',
    code: 'GB',
    dialCode: '+44',
    currency: 'GBP',
    currencySymbol: '£',
    flag: '🇬🇧'
  },
  {
    name: 'Kenya',
    code: 'KE',
    dialCode: '+254',
    currency: 'KES',
    currencySymbol: 'KSh',
    flag: '🇰🇪'
  },
  {
    name: 'Nigeria',
    code: 'NG',
    dialCode: '+234',
    currency: 'NGN',
    currencySymbol: '₦',
    flag: '🇳🇬'
  }
];

const seedCountries = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Clear existing countries
    await Country.deleteMany({});
    console.log('Cleared existing countries');

    // Insert new countries
    await Country.insertMany(countries);
    console.log('Countries seeded successfully');

    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  } catch (error) {
    console.error('Error seeding countries:', error);
    process.exit(1);
  }
};

// Run seeder if called directly
if (require.main === module) {
  seedCountries();
}

module.exports = seedCountries;
