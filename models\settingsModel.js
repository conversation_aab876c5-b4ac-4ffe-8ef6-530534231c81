const pool = require("../config/db");

const getCountries = async () => {
    const [result] = await pool.query(
      "SELECT * FROM ethiopay_countries"
    );
    return result;
  };

  const createBank = async (userData) => {
    const [result] =  await pool.query('INSERT INTO ethiopay_banks SET ?', userData);
    return result;
  };

 


  const findBankByName = async (name) => {
    const [result] = await pool.query('SELECT * FROM ethiopay_banks where name=?', name);
    return result[0];
  };
  const getBanks = async (baseUrl) => {
    const [result] = await pool.query('SELECT * FROM ethiopay_banks');
    return result.map(bank => ({
        ...bank,
        logo: `${baseUrl}/${bank.logo}`
    }));
  };

  module.exports = {
    getCountries,
    createBank,
    getBanks,
    findBankByName
  }