const Country = require('./schemas/Country');
const Bank = require('./schemas/Bank');

const getCountries = async () => {
  const countries = await Country.find({ isActive: true }).sort({ name: 1 });
  return countries;
};

const createBank = async (userData) => {
  const bank = new Bank(userData);
  const result = await bank.save();
  return result;
};

const findBankByName = async (name) => {
  const bank = await Bank.findOne({ name: name });
  return bank;
};

const getBanks = async (baseUrl) => {
  const banks = await Bank.find({ isActive: true })
    .populate('country', 'name code')
    .sort({ name: 1 });

  return banks.map(bank => ({
    ...bank.toObject(),
    logo: bank.logo ? `${baseUrl}/${bank.logo}` : null
  }));
};

module.exports = {
  getCountries,
  createBank,
  getBanks,
  findBankByName
}