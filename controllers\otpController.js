const otpService = require('../services/otpService');

const sendOTPSMS = async (req, res,next) => {
 try {
   await otpService.sendOTPSMS(req.body);
   res.success(null, 'Successfully sent!');
 } catch (error) {
   next(error);
 }
};

const sendOTPEmail = async (req, res,next) => {
  try {
    await otpService.sendOTPEmail(req.body);
    res.success(null, 'Successfully sent!');
  } catch (error) {
    next(error);
  }
 };


module.exports = {
    sendOTPSMS,
    sendOTPEmail
}

